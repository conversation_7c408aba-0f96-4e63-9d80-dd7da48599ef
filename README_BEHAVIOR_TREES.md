# Custom Behavior Tree Integration for ROS 2 Nav2

This document provides comprehensive guidance on integrating and using custom behavior tree XML files with your ROS 2 navigation system.

## Overview

This package includes several custom behavior tree XML files and launch configurations that allow you to use different navigation behaviors:

1. **Dynamic Following** (`follow_point.xml`) - For following dynamic points with path truncation
2. **Standard Navigation** (`navigate_to_pose_w_replanning_and_recovery.xml`) - Standard Nav2 behavior with replanning and recovery
3. **Simple Navigation** (`navigate_to_pose_simple.xml`) - Basic navigation without recovery behaviors
4. **Replanning Only** (`navigate_w_replanning_only.xml`) - Navigation with replanning but no recovery

## Project Structure

```
dynamic_following/
├── behavior_trees/
│   ├── follow_point.xml                                    # Dynamic following behavior tree
│   ├── navigate_to_pose_w_replanning_and_recovery.xml     # Standard Nav2 behavior tree
│   ├── navigate_through_poses_w_replanning_and_recovery.xml
│   ├── navigate_to_pose_simple.xml                        # Simple navigation (no recovery)
│   └── navigate_w_replanning_only.xml                     # Replanning without recovery
├── params/
│   ├── nav2_params_custom_bt.yaml                         # Standard behavior tree parameters
│   ├── nav2_params_dynamic_following.yaml                 # Dynamic following parameters
│   └── nav2_params.yaml                                   # Original parameters
├── launch/
│   ├── tb3_simulation_following_launch.py                 # Main launch file (dynamic following)
│   ├── tb3_simulation_standard_bt_launch.py               # Standard behavior tree launch
│   ├── tb3_simulation_simple_bt_launch.py                 # Simple behavior tree launch
│   └── tb3_simulation_replanning_only_launch.py           # Replanning-only launch
└── README_BEHAVIOR_TREES.md                               # This documentation
```

## Quick Start

### 1. Build the Package

```bash
cd ~/your_workspace
colcon build --packages-select dynamic_following
source install/setup.bash
```

### 2. Launch Different Behavior Tree Modes

#### Dynamic Following (Default)
```bash
ros2 launch dynamic_following tb3_simulation_following_launch.py
```

#### Standard Nav2 Behavior Tree
```bash
ros2 launch dynamic_following tb3_simulation_standard_bt_launch.py
```

#### Simple Navigation (No Recovery)
```bash
ros2 launch dynamic_following tb3_simulation_simple_bt_launch.py
```

#### Replanning Only
```bash
ros2 launch dynamic_following tb3_simulation_replanning_only_launch.py
```

## Custom Launch Arguments

The main launch file supports several arguments for customization:

```bash
ros2 launch dynamic_following tb3_simulation_following_launch.py \
    behavior_tree_mode:=dynamic_following \
    default_nav_to_pose_bt_xml:=/path/to/your/custom_bt.xml \
    params_file:=/path/to/your/custom_params.yaml \
    use_simulator:=True \
    headless:=False
```

### Available Arguments:

- `behavior_tree_mode`: `dynamic_following`, `standard`, `simple`, `replanning_only`
- `default_nav_to_pose_bt_xml`: Path to custom navigate-to-pose behavior tree XML
- `default_nav_through_poses_bt_xml`: Path to custom navigate-through-poses behavior tree XML
- `params_file`: Path to custom navigation parameters file
- `use_simulator`: Whether to start Gazebo simulation
- `headless`: Whether to run Gazebo without GUI
- `use_rviz`: Whether to start RViz
- `slam`: Whether to use SLAM instead of localization

## Testing the Integration

### Automated Testing

Run the automated test script to verify your behavior tree integration:

```bash
# Build and source the package first
colcon build --packages-select dynamic_following
source install/setup.bash

# Run the automated test
ros2 run dynamic_following test_behavior_trees
```

This script will check:
- ✓ Behavior tree XML files exist and are valid
- ✓ bt_navigator parameters are configured correctly
- ✓ Navigation action server is available
- ✓ Test goal can be sent successfully

### 1. Basic Functionality Test

1. Launch the simulation:
   ```bash
   ros2 launch dynamic_following tb3_simulation_following_launch.py
   ```

2. Wait for all nodes to start (you should see the robot in Gazebo and RViz)

3. Set an initial pose in RViz using the "2D Pose Estimate" tool

4. Set a navigation goal using the "2D Nav Goal" tool

5. Observe the robot navigating to the goal

### 2. Behavior Tree Verification

Check that your custom behavior tree is loaded:

```bash
# Check the bt_navigator parameters
ros2 param get /bt_navigator default_nav_to_pose_bt_xml

# Monitor behavior tree execution
ros2 topic echo /behavior_tree_log
```

### 3. Dynamic Following Test

For the dynamic following behavior tree:

1. Launch with dynamic following mode:
   ```bash
   ros2 launch dynamic_following tb3_simulation_following_launch.py
   ```

2. Publish dynamic goals to test path truncation:
   ```bash
   # In another terminal, publish a goal
   ros2 topic pub /goal_pose geometry_msgs/PoseStamped "
   header:
     frame_id: 'map'
   pose:
     position: {x: 2.0, y: 1.0, z: 0.0}
     orientation: {w: 1.0}"
   ```

3. Observe that the robot follows the path but stops at the truncated distance (1.0m by default)

## Troubleshooting

### Common Issues and Solutions

#### 1. Behavior Tree XML Not Found
**Error**: `Failed to load behavior tree from file`

**Solution**: 
- Check that the XML file path is correct
- Ensure the package is built and sourced
- Verify file permissions

#### 2. Invalid Behavior Tree XML
**Error**: `XML parsing error` or `Unknown node type`

**Solution**:
- Validate XML syntax
- Check that all BT node plugins are loaded
- Verify node names match the plugin library names

#### 3. Navigation Not Working
**Symptoms**: Robot doesn't move or gets stuck

**Solution**:
- Check costmap configuration
- Verify transforms are published correctly
- Check goal tolerance settings
- Monitor `/cmd_vel` topic for velocity commands

#### 4. Recovery Behaviors Not Executing
**Symptoms**: Robot gives up without trying recovery

**Solution**:
- Check `number_of_retries` parameter in behavior tree
- Verify recovery behavior plugins are loaded
- Check error codes in behavior tree execution

### Debugging Commands

```bash
# Check active nodes
ros2 node list

# Monitor navigation status
ros2 topic echo /navigate_to_pose/_action/status

# Check behavior tree execution
ros2 topic echo /behavior_tree_log

# Monitor robot velocity
ros2 topic echo /cmd_vel

# Check transforms
ros2 run tf2_tools view_frames.py

# Check costmaps
ros2 topic echo /local_costmap/costmap
ros2 topic echo /global_costmap/costmap
```

## Customizing Behavior Trees

### Creating Your Own Behavior Tree

1. Create a new XML file in the `behavior_trees/` directory
2. Follow the BehaviorTree.CPP v4 format
3. Use available Nav2 BT node plugins
4. Update the parameters file to reference your new BT
5. Test thoroughly in simulation

### Example Custom Behavior Tree Structure

```xml
<root BTCPP_format="4" main_tree_to_execute="MainTree">
    <BehaviorTree ID="MainTree">
        <!-- Your custom behavior tree logic here -->
    </BehaviorTree>
</root>
```

## Performance Tuning

### Behavior Tree Parameters

- `bt_loop_duration`: How often the BT is ticked (default: 10ms)
- `default_server_timeout`: Timeout for action servers (default: 20s)
- `wait_for_service_timeout`: Timeout for service calls (default: 1000ms)

### Rate Controllers

Adjust planning frequency in your behavior trees:
```xml
<RateController hz="1.0">  <!-- Plan at 1 Hz -->
    <ComputePathToPose .../>
</RateController>
```

## Next Steps

1. Experiment with different behavior tree configurations
2. Create custom BT nodes for specific behaviors
3. Integrate with your robot's specific sensors and actuators
4. Test in real-world scenarios

For more information, refer to the [Nav2 Behavior Trees documentation](https://docs.nav2.org/behavior_trees/index.html).
