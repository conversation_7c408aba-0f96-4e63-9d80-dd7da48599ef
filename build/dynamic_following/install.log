/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/__init__.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/test_behavior_trees.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following/__init__.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/__pycache__/__init__.cpython-310.pyc
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/__pycache__/test_behavior_trees.cpython-310.pyc
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following/__pycache__/__init__.cpython-310.pyc
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index/packages/dynamic_following
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/package.xml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch/tb3_simulation_following_launch.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch/tb3_simulation_standard_bt_launch.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch/tb3_simulation_simple_bt_launch.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch/tb3_simulation_replanning_only_launch.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch/test_minimal_nav2.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params/nav2_params.yaml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params/nav2_params_custom_bt.yaml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params/nav2_params_dynamic_following.yaml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params/nav2_multirobot_params_1.yaml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params/nav2_multirobot_params_2.yaml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params/nav2_multirobot_params_all.yaml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees/follow_point.xml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees/navigate_to_pose_simple.xml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees/navigate_w_replanning_only.xml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps/turtlebot3_world.yaml
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps/turtlebot3_world.pgm
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz/nav2_default_view.rviz
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz/nav2_namespaced_view.rviz
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/urdf/turtlebot3_waffle.urdf
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds/waffle.model
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds/world_only.model
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/scripts/test_behavior_trees.py
/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/README_BEHAVIOR_TREES.md
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info/PKG-INFO
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info/top_level.txt
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info/dependency_links.txt
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info/SOURCES.txt
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info/requires.txt
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info/zip-safe
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info/entry_points.txt
/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following/test_behavior_trees
