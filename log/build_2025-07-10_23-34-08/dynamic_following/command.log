Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/projects/dynamic_following' returned '0': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
