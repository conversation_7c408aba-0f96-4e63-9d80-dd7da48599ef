# Dynamic Following with Custom Behavior Trees

This ROS 2 package demonstrates how to integrate custom behavior tree XML files with Nav2 for dynamic following behavior.

## Overview

The package includes:
- Custom behavior tree XML files for navigation
- Modified launch files that load custom behavior trees
- Custom Nav2 parameter configuration
- Integration with TurtleBot3 simulation

## Project Structure

```
dynamic_following/
├── behavior_trees/
│   ├── follow_point.xml              # Custom BT for following a dynamic point
│   └── navigate_through_poses.xml    # Custom BT for navigating through multiple poses
├── config/
│   └── nav2_params.yaml              # Custom Nav2 parameters
├── launch/
│   └── tb3_simulation_following_launch.py  # Modified launch file
└── README.md
```

## Behavior Trees

### 1. follow_point.xml
This behavior tree is designed for following a dynamic pose to a certain distance. It includes:
- **PipelineSequence**: Main navigation sequence
- **ControllerSelector**: Selects the path following controller
- **PlannerSelector**: Selects the path planning algorithm
- **RateController**: Controls the execution rate (1 Hz)
- **GoalUpdater**: Updates goals dynamically
- **ComputePathToPose**: Computes path to the target pose
- **TruncatePath**: Truncates the path to maintain following distance (1.0m)
- **FollowPath**: Executes path following

### 2. navigate_through_poses.xml
This behavior tree handles navigation through multiple waypoints with recovery behaviors:
- **RecoveryNode**: Provides retry mechanism (6 retries)
- **RemovePassedGoals**: Removes completed waypoints
- **ComputePathThroughPoses**: Plans path through multiple poses
- **Recovery Actions**: Includes costmap clearing, spinning, waiting, and backing up

## Usage

### Building the Package

```bash
cd ~/ros2_ws
colcon build --packages-select dynamic_following
source install/setup.bash
```

### Launching with Custom Behavior Trees

#### Basic Launch (uses default behavior trees)
```bash
ros2 launch dynamic_following tb3_simulation_following_launch.py
```

#### Launch with Custom Behavior Tree for nav_to_pose
```bash
ros2 launch dynamic_following tb3_simulation_following_launch.py \
    bt_xml_file:=/path/to/your/custom_bt.xml
```

#### Launch with Custom Behavior Trees for both nav_to_pose and nav_through_poses
```bash
ros2 launch dynamic_following tb3_simulation_following_launch.py \
    bt_xml_file:=/path/to/your/nav_to_pose_bt.xml \
    bt_xml_through_poses_file:=/path/to/your/nav_through_poses_bt.xml
```

#### Launch with Custom Parameters
```bash
ros2 launch dynamic_following tb3_simulation_following_launch.py \
    params_file:=/path/to/your/custom_nav2_params.yaml
```

### Available Launch Arguments

| Argument | Default | Description |
|----------|---------|-------------|
| `bt_xml_file` | `follow_point.xml` | Path to behavior tree XML for nav_to_pose |
| `bt_xml_through_poses_file` | `navigate_through_poses.xml` | Path to behavior tree XML for nav_through_poses |
| `params_file` | `config/nav2_params.yaml` | Path to Nav2 parameters file |
| `namespace` | `""` | Robot namespace |
| `use_sim_time` | `true` | Use simulation time |
| `slam` | `false` | Enable SLAM |
| `map` | Default TB3 map | Map file path |
| `autostart` | `true` | Auto-start navigation |
| `use_rviz` | `true` | Launch RViz |
| `headless` | `true` | Run Gazebo headless |

## Creating Custom Behavior Trees

### Basic Structure
All behavior tree XML files should follow this structure:

```xml
<root BTCPP_format="4" main_tree_to_execute="MainTree">
    <BehaviorTree ID="MainTree">
        <!-- Your behavior tree nodes here -->
    </BehaviorTree>
</root>
```

### Available Nav2 Behavior Tree Nodes

The package includes all standard Nav2 BT nodes:
- **Action Nodes**: ComputePathToPose, FollowPath, Spin, BackUp, etc.
- **Condition Nodes**: GoalReached, IsStuck, PathValid, etc.
- **Control Nodes**: PipelineSequence, RecoveryNode, RateController, etc.
- **Decorator Nodes**: KeepRunningUntilFailure, etc.

### Key Parameters for Custom Behavior Trees

- `{goal}`: Current navigation goal
- `{path}`: Computed path
- `{selected_controller}`: Active controller plugin
- `{selected_planner}`: Active planner plugin
- `error_code_id` and `error_msg`: Error handling parameters

## Integration Details

### How Custom Behavior Trees are Loaded

1. **Launch File**: The launch file declares behavior tree XML file paths as arguments
2. **Parameter Rewriting**: Uses `RewrittenYaml` to inject BT XML paths into Nav2 parameters
3. **BT Navigator**: The bt_navigator node loads the specified XML files at runtime

### Parameter Configuration

The custom `nav2_params.yaml` includes:
- Behavior tree navigator configuration
- Controller server settings (DWB local planner)
- Planner server settings (NavFn global planner)
- Costmap configurations (local and global)
- Recovery behavior settings

### File Installation

The `setup.py` ensures all files are properly installed:
- Behavior tree XML files → `share/dynamic_following/behavior_trees/`
- Configuration files → `share/dynamic_following/config/`
- Launch files → `share/dynamic_following/launch/`

## Testing

### Verify Behavior Tree Loading
Check that your custom behavior trees are loaded:

```bash
ros2 param get /bt_navigator default_nav_to_pose_bt_xml
ros2 param get /bt_navigator default_nav_through_poses_bt_xml
```

### Monitor Behavior Tree Execution
Use the BT navigator logs to monitor execution:

```bash
ros2 topic echo /bt_navigator/transition_event
```

## Troubleshooting

### Common Issues

1. **BT XML not found**: Ensure file paths are correct and files are installed
2. **BT parsing errors**: Validate XML syntax and node names
3. **Missing BT plugins**: Check that all required BT node plugins are loaded
4. **Parameter loading issues**: Verify YAML syntax and parameter names

### Debug Commands

```bash
# Check if behavior tree files exist
ls $(ros2 pkg prefix dynamic_following)/share/dynamic_following/behavior_trees/

# Verify parameter loading
ros2 param list /bt_navigator

# Check BT navigator status
ros2 node info /bt_navigator
```

## Next Steps

- Modify the existing behavior trees to suit your specific use case
- Create additional behavior tree variants for different scenarios
- Integrate with custom action servers or condition nodes
- Add behavior tree visualization and monitoring tools
