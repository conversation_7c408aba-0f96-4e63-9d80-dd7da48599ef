<!--
  This Behavior Tree navigates through multiple poses with dynamic following behavior
-->

<root BTCPP_format="4" main_tree_to_execute="MainTree">
    <BehaviorTree ID="MainTree">
        <PipelineSequence name="NavigateThroughPoses">
            <ControllerSelector selected_controller="{selected_controller}" default_controller="FollowPath" topic_name="controller_selector"/>
            <PlannerSelector selected_planner="{selected_planner}" default_planner="GridBased" topic_name="planner_selector"/>
            <RateController hz="1.0">
                <RecoveryNode number_of_retries="6" name="RecoveryNode">
                    <PipelineSequence name="NavigateWithReplanning">
                        <RemovePassedGoals input_goals="{goals}" output_goals="{updated_goals}" radius="0.7"/>
                        <ComputePathThroughPoses goals="{updated_goals}" path="{path}" planner_id="{selected_planner}" error_code_id="{compute_path_error_code}" error_msg="{compute_path_error_msg}"/>
                        <TruncatePath distance="1.0" input_path="{path}" output_path="{truncated_path}"/>
                        <FollowPath path="{truncated_path}" controller_id="{selected_controller}" error_code_id="{follow_path_error_code}" error_msg="{follow_path_error_msg}"/>
                    </PipelineSequence>
                    <ReactiveFallback name="RecoveryFallback">
                        <GoalUpdated/>
                        <SequenceStar name="RecoveryActions">
                            <ClearEntireCostmap name="ClearGlobalCostmap-Context" service_name="global_costmap/clear_entirely_global_costmap"/>
                            <ClearEntireCostmap name="ClearLocalCostmap-Context" service_name="local_costmap/clear_entirely_local_costmap"/>
                            <Spin spin_dist="1.57" error_code_id="{spin_error_code}" error_msg="{spin_error_msg}"/>
                            <Wait wait_duration="5"/>
                            <BackUp backup_dist="0.30" backup_speed="0.05" error_code_id="{backup_error_code}" error_msg="{backup_error_msg}"/>
                        </SequenceStar>
                    </ReactiveFallback>
                </RecoveryNode>
            </RateController>
        </PipelineSequence>
    </BehaviorTree>
</root>
