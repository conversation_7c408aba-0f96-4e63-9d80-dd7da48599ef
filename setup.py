from setuptools import find_packages, setup

package_name = "dynamic_following"

setup(
    name=package_name,
    version="0.0.0",
    packages=find_packages(exclude=["test"]),
    data_files=[
        ("share/ament_index/resource_index/packages", ["resource/" + package_name]),
        ("share/" + package_name, ["package.xml", "README.md"]),
        (
            "share/" + package_name + "/launch",
            ["launch/tb3_simulation_following_launch.py"],
        ),
        (
            "share/" + package_name + "/behavior_trees",
            [
                "behavior_trees/follow_point.xml",
                "behavior_trees/navigate_through_poses.xml",
            ],
        ),
        ("share/" + package_name + "/config", ["config/nav2_params.yaml"]),
    ],
    install_requires=["setuptools"],
    zip_safe=True,
    maintainer="heting",
    maintainer_email="<EMAIL>",
    description="TODO: Package description",
    license="TODO: License declaration",
    tests_require=["pytest"],
    entry_points={
        "console_scripts": [],
    },
)
