from setuptools import find_packages, setup

package_name = "dynamic_following"

setup(
    name=package_name,
    version="0.0.0",
    packages=find_packages(exclude=["test"]),
    data_files=[
        ("share/ament_index/resource_index/packages", ["resource/" + package_name]),
        ("share/" + package_name, ["package.xml"]),
        (
            "share/" + package_name + "/launch",
            [
                "launch/tb3_simulation_following_launch.py",
                "launch/tb3_simulation_standard_bt_launch.py",
                "launch/tb3_simulation_simple_bt_launch.py",
                "launch/tb3_simulation_replanning_only_launch.py",
            ],
        ),
        (
            "share/" + package_name + "/params",
            [
                "params/nav2_params.yaml",
                "params/nav2_params_custom_bt.yaml",
                "params/nav2_params_dynamic_following.yaml",
                "params/nav2_multirobot_params_1.yaml",
                "params/nav2_multirobot_params_2.yaml",
                "params/nav2_multirobot_params_all.yaml",
            ],
        ),
        (
            "share/" + package_name + "/behavior_trees",
            [
                "behavior_trees/follow_point.xml",
                "behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml",
                "behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml",
                "behavior_trees/navigate_to_pose_simple.xml",
                "behavior_trees/navigate_w_replanning_only.xml",
            ],
        ),
        (
            "share/" + package_name + "/maps",
            ["maps/turtlebot3_world.yaml", "maps/turtlebot3_world.pgm"],
        ),
        (
            "share/" + package_name + "/rviz",
            ["rviz/nav2_default_view.rviz", "rviz/nav2_namespaced_view.rviz"],
        ),
        ("share/" + package_name + "/urdf", ["urdf/turtlebot3_waffle.urdf"]),
        (
            "share/" + package_name + "/worlds",
            ["worlds/waffle.model", "worlds/world_only.model"],
        ),
        (
            "share/" + package_name + "/scripts",
            ["scripts/test_behavior_trees.py"],
        ),
        (
            "share/" + package_name,
            ["README_BEHAVIOR_TREES.md"],
        ),
    ],
    install_requires=["setuptools"],
    zip_safe=True,
    maintainer="heting",
    maintainer_email="<EMAIL>",
    description="TODO: Package description",
    license="TODO: License declaration",
    tests_require=["pytest"],
    entry_points={
        "console_scripts": [
            "test_behavior_trees = scripts.test_behavior_trees:main",
        ],
    },
)
